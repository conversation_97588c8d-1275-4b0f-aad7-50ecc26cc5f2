.url-list {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  padding: 40px;
  border: 1px solid #e2e8f0;
  backdrop-filter: blur(10px);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 20px;
}

.list-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.5px;
}

.list-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.stat-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.stat-badge svg {
  color: rgba(255, 255, 255, 0.9);
}

.urls-container {
  position: relative;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
}

.empty-icon {
  margin-bottom: 24px;
  opacity: 0.3;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.empty-state p {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
}

.urls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
  gap: 28px;
}

.url-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  padding: 28px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.url-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  border-radius: 20px 20px 0 0;
}

.url-card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.03) 0%, transparent 70%);
  pointer-events: none;
  transition: all 0.4s ease;
}

.url-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: #c7d2fe;
}

.url-card:hover::after {
  top: -20%;
  right: -20%;
}

.url-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.url-info {
  flex: 1;
  min-width: 0;
}

.original-url {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 4px;
}

.original-url svg {
  stroke: currentColor;
  fill: none;
  flex-shrink: 0;
}

.url-domain {
  font-weight: 500;
  color: #374151;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.lock-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 6px;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  transition: all 0.3s ease;
}

.lock-indicator:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.lock-indicator svg {
  stroke: currentColor;
  fill: none;
}

.url-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-left: 16px;
}

.url-date {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 6px;
}

.url-stats {
  flex-shrink: 0;
}

.click-count {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  white-space: nowrap;
  transition: all 0.3s ease;
}

.click-count:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.click-count svg {
  color: rgba(255, 255, 255, 0.9);
}

.short-url-section {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.short-url-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.short-url-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.short-url-container:hover::before {
  opacity: 1;
}

.short-url-container:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #c7d2fe;
  transform: translateY(-2px);
}

.short-url-actions {
  display: flex;
  gap: 14px;
  align-items: center;
  justify-content: flex-start;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 14px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.short-url-actions:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
  border-color: #c7d2fe;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
}

.short-url-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2, #8b5cf6);
  border-radius: 14px 14px 0 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.short-url-actions:hover::before {
  opacity: 1;
}

.short-url-actions::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(102, 126, 234, 0.02) 0%, transparent 50%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.short-url-actions:hover::after {
  opacity: 1;
}

.short-url-link {
  flex: 1;
  color: #667eea;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  word-break: break-all;
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.short-url-link:hover {
  transform: translateX(4px);
}

.short-url-link svg {
  color: #667eea;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.copy-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.copy-btn:hover::before {
  left: 100%;
}

.copy-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.copy-btn.copied {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  animation: successPulse 0.6s ease-out;
}

.short-url-actions .qr-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  border: none;
  color: white;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;
}

.short-url-actions .qr-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.short-url-actions .qr-btn:hover::before {
  left: 100%;
}

.short-url-actions .qr-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

.short-url-actions .qr-btn svg {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.short-url-actions .qr-btn:hover svg {
  transform: scale(1.1);
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@media (max-width: 768px) {
  .url-list {
    padding: 28px;
  }
  
  .list-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .list-header h2 {
    font-size: 2rem;
  }
  
  .list-stats {
    align-self: stretch;
    justify-content: space-between;
  }
  
  .urls-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .url-card {
    padding: 24px;
  }
  
  .url-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .url-stats {
    align-self: flex-start;
  }
  
  .short-url-container {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .short-url-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    padding: 14px 16px;
    flex-wrap: wrap;
  }
  
  .copy-btn {
    justify-content: center;
    flex: 1;
  }
  
  .short-url-actions .qr-btn {
    flex: 0 0 auto;
  }
}

@media (max-width: 480px) {
  .url-list {
    padding: 24px;
  }
  
  .list-header h2 {
    font-size: 1.75rem;
  }
  
  .stat-badge {
    font-size: 0.8125rem;
    padding: 10px 16px;
  }
  
  .url-card {
    padding: 20px;
  }
  
  .empty-state {
    padding: 60px 20px;
  }
  
  .empty-state h3 {
    font-size: 1.25rem;
  }
  
  .urls-grid {
    grid-template-columns: 1fr;
  }
}

/* New styles for active/inactive status and toggle functionality */
.url-card.disabled {
  opacity: 0.7;
  border-color: #fca5a5;
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.url-card.disabled::before {
  background: linear-gradient(90deg, #ef4444, #dc2626, #b91c1c, #991b1b);
}

.status-indicator {
  font-size: 1.2rem;
  font-weight: bold;
  flex-shrink: 0;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.status-indicator.active {
  color: #10b981;
  animation: pulse 2s infinite;
}

.status-indicator.inactive {
  color: #ef4444;
  opacity: 0.7;
}

.password-attempts {
  font-size: 0.75rem;
  color: #dc2626;
  font-weight: 600;
  background: #fef2f2;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #fecaca;
}

.disabled-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border: 1px solid #fca5a5;
  border-radius: 12px;
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: -8px;
}

.disabled-warning svg {
  color: #dc2626;
  flex-shrink: 0;
}

.toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  min-width: 44px;
  height: 44px;
}

.toggle-btn.active {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.toggle-btn.inactive {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.toggle-btn:hover::before {
  left: 100%;
}

.toggle-btn:hover {
  transform: translateY(-3px);
}

.toggle-btn.active:hover {
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.toggle-btn.inactive:hover {
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.toggle-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.reset-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 44px;
  height: 44px;
}

.reset-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.reset-btn:hover::before {
  left: 100%;
}

.reset-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .short-url-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .toggle-btn,
  .reset-btn {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .url-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding-left: 12px;
  }
  
  .disabled-warning {
    font-size: 0.8125rem;
    padding: 10px 12px;
  }
  
  .short-url-actions {
    justify-content: space-between;
    padding: 12px;
    gap: 8px;
  }
  
  .toggle-btn,
  .reset-btn,
  .short-url-actions .qr-btn {
    padding: 8px;
    min-width: 36px;
    height: 36px;
  }
} 