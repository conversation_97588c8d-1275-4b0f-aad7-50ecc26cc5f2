<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Protected Link</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 48px;
            max-width: 480px;
            width: 100%;
            box-shadow: 0 20px 64px rgba(0, 0, 0, 0.1);
            text-align: center;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .lock-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        p {
            font-size: 1rem;
            color: #6b7280;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        .form-group {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .password-input {
            flex: 1;
            padding: 16px 20px;
            font-size: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #f8fafc;
            color: #1f2937;
            outline: none;
            transition: all 0.3s ease;
        }

        .password-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .unlock-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }

        .unlock-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .unlock-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .error-message {
            color: #dc2626;
            font-size: 0.875rem;
            font-weight: 500;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
        }

        .loading {
            display: none;
        }

        .loading.active {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
            .container {
                padding: 32px 24px;
            }
            
            .form-group {
                flex-direction: column;
            }
            
            .unlock-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="lock-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
            </svg>
        </div>
        
        <h1>Password Protected Link</h1>
        <p>This link is password protected. Enter the password to continue.</p>
        
        <form id="passwordForm">
            <div class="form-group">
                <input 
                    type="password" 
                    id="passwordInput" 
                    class="password-input" 
                    placeholder="Enter password"
                    required
                    autofocus
                />
                <button type="submit" class="unlock-btn" id="unlockBtn">
                    <span class="loading" id="loading"></span>
                    <span id="buttonText">Unlock</span>
                </button>
            </div>
        </form>
        
        <div id="errorMessage" class="error-message" style="display: none;"></div>
    </div>

    <script>
        const form = document.getElementById('passwordForm');
        const passwordInput = document.getElementById('passwordInput');
        const unlockBtn = document.getElementById('unlockBtn');
        const loading = document.getElementById('loading');
        const buttonText = document.getElementById('buttonText');
        const errorMessage = document.getElementById('errorMessage');
        
        // Get shortUrl from current path
        const shortUrl = window.location.pathname.substring(1);
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const password = passwordInput.value.trim();
            if (!password) return;
            
            // Show loading state
            loading.classList.add('active');
            buttonText.textContent = 'Unlocking...';
            unlockBtn.disabled = true;
            errorMessage.style.display = 'none';
            
            try {
                const response = await fetch('/api/verify-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ shortUrl, password }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Success - redirect to original URL
                    window.location.href = data.originalUrl;
                } else {
                    // Show error
                    errorMessage.textContent = data.error || 'Invalid password';
                    errorMessage.style.display = 'block';
                    
                    // Reset form
                    passwordInput.value = '';
                    passwordInput.focus();
                }
            } catch (error) {
                errorMessage.textContent = 'Failed to verify password. Please try again.';
                errorMessage.style.display = 'block';
            } finally {
                // Hide loading state
                loading.classList.remove('active');
                buttonText.textContent = 'Unlock';
                unlockBtn.disabled = false;
            }
        });
    </script>
</body>
</html> 