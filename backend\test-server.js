import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 8000;

app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Backend is running!' });
});

app.post('/api/auth/login', (req, res) => {
  console.log('Login request received:', req.body);
  res.json({ 
    user: { id: '123', email: '<EMAIL>' },
    token: 'test-token'
  });
});

app.post('/api/auth/register', (req, res) => {
  console.log('Register request received:', req.body);
  res.json({ 
    user: { id: '123', email: '<EMAIL>' },
    token: 'test-token'
  });
});

app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
});
