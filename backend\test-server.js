const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

console.log('Setting up middleware...');
app.use(cors());
app.use(express.json());

console.log('Setting up routes...');
app.get('/', (req, res) => {
  console.log('GET / request received');
  res.json({ message: 'Backend is running!' });
});

app.post('/api/auth/login', (req, res) => {
  console.log('Login request received:', req.body);
  res.json({
    user: { id: '123', email: req.body.email || '<EMAIL>' },
    token: 'test-token-' + Date.now()
  });
});

app.post('/api/auth/register', (req, res) => {
  console.log('Register request received:', req.body);
  res.json({
    user: { id: '123', email: req.body.email || '<EMAIL>' },
    token: 'test-token-' + Date.now()
  });
});

console.log('Starting server...');
app.listen(PORT, () => {
  console.log(`✅ Test server running on http://localhost:${PORT}`);
  console.log('✅ CORS enabled');
  console.log('✅ JSON parsing enabled');
});
