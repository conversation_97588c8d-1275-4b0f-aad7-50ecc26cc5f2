.password-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(4px);
}

.password-modal {
  background: white;
  border-radius: 20px;
  padding: 40px;
  max-width: 480px;
  width: 100%;
  box-shadow: 0 20px 64px rgba(0, 0, 0, 0.2);
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.password-modal-header {
  text-align: center;
  margin-bottom: 32px;
}

.lock-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.password-modal-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.password-modal-header p {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.5;
}

.password-form {
  margin-bottom: 24px;
}

.password-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;
}

.password-modal-input {
  flex: 1;
  padding: 16px 20px;
  font-size: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: #f8fafc;
  color: #1f2937;
  outline: none;
  transition: all 0.3s ease;
  font-weight: 400;
}

.password-modal-input::placeholder {
  color: #9ca3af;
}

.password-modal-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: white;
}

.unlock-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  white-space: nowrap;
  flex-shrink: 0;
}

.unlock-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.unlock-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.password-error {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 500;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}

.password-error svg {
  stroke: currentColor;
  fill: none;
  flex-shrink: 0;
}

.attempt-count {
  color: #991b1b;
  font-weight: 600;
  margin-left: 4px;
}

.password-modal-footer {
  display: flex;
  justify-content: center;
}

.cancel-btn {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 8px;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.close-btn svg {
  stroke: currentColor;
}

@media (max-width: 480px) {
  .password-modal {
    padding: 32px 24px;
    margin: 20px;
  }
  
  .password-modal-header h2 {
    font-size: 1.25rem;
  }
  
  .password-input-group {
    flex-direction: column;
    gap: 12px;
  }
  
  .unlock-btn {
    width: 100%;
    justify-content: center;
  }
} 